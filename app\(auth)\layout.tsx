export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-950 to-black relative overflow-hidden">
      {/* Enhanced Grid System with homepage consistency */}
      <div className="absolute inset-0 opacity-[0.04]">
        {/* Horizontal grid lines */}
        <div className="absolute top-[16.666%] left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/90 to-transparent"></div>
        <div className="absolute top-[33.333%] left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-400/80 to-transparent"></div>
        <div className="absolute top-[50%] left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/90 to-transparent"></div>
        <div className="absolute top-[66.666%] left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/80 to-transparent"></div>
        <div className="absolute top-[83.333%] left-0 w-full h-px bg-gradient-to-r from-transparent via-red-400/80 to-transparent"></div>

        {/* Vertical grid lines */}
        <div className="absolute top-0 left-[16.666%] w-px h-full bg-gradient-to-b from-transparent via-orange-500/90 to-transparent"></div>
        <div className="absolute top-0 left-[33.333%] w-px h-full bg-gradient-to-b from-transparent via-orange-400/80 to-transparent"></div>
        <div className="absolute top-0 left-[50%] w-px h-full bg-gradient-to-b from-transparent via-red-500/90 to-transparent"></div>
        <div className="absolute top-0 left-[66.666%] w-px h-full bg-gradient-to-b from-transparent via-orange-500/80 to-transparent"></div>
        <div className="absolute top-0 left-[83.333%] w-px h-full bg-gradient-to-b from-transparent via-red-400/80 to-transparent"></div>
      </div>

      {/* Dynamic Background Orbs */}
      <div className="absolute top-1/5 left-1/12 w-72 h-72 bg-gradient-to-br from-orange-500/12 to-red-600/8 rounded-full blur-3xl opacity-70 animate-float"></div>
      <div className="absolute bottom-1/5 right-1/12 w-80 h-80 bg-gradient-to-br from-red-400/10 to-orange-500/6 rounded-full blur-3xl opacity-60 animate-float-delayed"></div>
      <div className="absolute top-2/3 left-1/2 transform -translate-x-1/2 w-[35rem] h-[35rem] bg-gradient-to-br from-orange-500/6 to-red-500/4 rounded-full blur-3xl opacity-50 animate-pulse-slow"></div>

      {/* Additional floating elements */}
      <div className="absolute top-1/8 right-1/5 w-28 h-28 bg-gradient-to-br from-orange-400/15 to-red-400/10 rounded-full blur-2xl opacity-70 animate-float-slow"></div>
      <div className="absolute bottom-1/8 left-1/5 w-36 h-36 bg-gradient-to-br from-red-400/12 to-orange-500/8 rounded-full blur-2xl opacity-60 animate-float-reverse"></div>
      
      {/* Subtle corner accents */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-orange-500/8 to-transparent rounded-full blur-2xl opacity-40"></div>
      <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-red-500/8 to-transparent rounded-full blur-2xl opacity-40"></div>

      {/* Main content container */}
      <div className="relative z-20 flex justify-center h-screen pt-16 pb-4 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="w-full max-w-md">
          {children}
        </div>
      </div>
      
      {/* Subtle overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none"></div>
    </div>
  );
}
