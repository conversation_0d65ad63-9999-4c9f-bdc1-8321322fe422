import { Link } from "@heroui/link";
import { Button } from "@heroui/button";
import { CheckCircleIcon } from "@/components/icons";
import { AuthCard } from "@/components/auth/AuthCard";

export default function SuccessPage() {
  return (
    <AuthCard
      title="Welcome to StaticHost!"
      subtitle="Your account has been created successfully"
    >
      <div className="text-center space-y-6">
        <div className="flex justify-center">
          <div className="p-4 rounded-full bg-green-500/20 border border-green-500/30">
            <CheckCircleIcon size={48} className="text-green-400" />
          </div>
        </div>
        
        <div className="space-y-3">
          <p className="text-gray-300">
            You're all set! Start deploying your static sites with lightning speed.
          </p>
          <div className="text-sm text-gray-400">
            <p>✓ Free SSL certificates included</p>
            <p>✓ Global CDN deployment</p>
            <p>✓ Custom domain support</p>
          </div>
        </div>

        <div className="space-y-3">
          <Button
            as={Link}
            href="/dashboard"
            className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-lg shadow-orange-500/25"
            size="lg"
          >
            Go to Dashboard
          </Button>
          
          <Button
            as={Link}
            href="/docs"
            variant="bordered"
            className="w-full border-gray-600 text-gray-300 hover:border-gray-500 hover:text-white"
            size="lg"
          >
            View Documentation
          </Button>
        </div>
      </div>
    </AuthCard>
  );
}
