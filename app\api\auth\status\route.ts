import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'

export async function GET(request: NextRequest) {
  try {
    // Get session ID from cookies
    const sessionId = request.cookies.get('sessionId')?.value
    
    if (!sessionId) {
      return NextResponse.json({ authenticated: false, user: null })
    }
    
    // Get user from session
    const user = getSession(sessionId)
    
    if (!user) {
      return NextResponse.json({ authenticated: false, user: null })
    }
    
    return NextResponse.json({ authenticated: true, user })
  } catch (error) {
    console.error('Auth status error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}