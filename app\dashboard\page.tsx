import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { StatsOverview } from "@/components/dashboard/StatsOverview";
import { ProjectsList } from "@/components/dashboard/ProjectsList";
import { RecentActivity } from "@/components/dashboard/RecentActivity";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { UsageMetrics } from "@/components/dashboard/UsageMetrics";

export default function DashboardPage() {
  return (
    <div className="flex-1 bg-black relative overflow-hidden">
      {/* Subtle background grid for consistency */}
      <div className="absolute inset-0 opacity-2">
        <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/60 to-transparent"></div>
        <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/40 to-transparent"></div>
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-orange-400/40 to-transparent"></div>
        <div className="absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-transparent via-red-400/40 to-transparent"></div>
      </div>

      <div className="relative z-10">
        {/* Dashboard Header */}
        <DashboardHeader />

        {/* Main Dashboard Content */}
        <main className="container mx-auto px-6 py-8 max-w-7xl">
          {/* Stats Overview */}
          <section className="mb-8">
            <StatsOverview />
          </section>

          {/* Quick Actions */}
          <section className="mb-8">
            <QuickActions />
          </section>

          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            {/* Projects List - Takes 2 columns on large screens */}
            <div className="lg:col-span-2">
              <ProjectsList />
            </div>

            {/* Recent Activity - Takes 1 column */}
            <div className="lg:col-span-1">
              <RecentActivity />
            </div>
          </div>

          {/* Usage Metrics */}
          <section>
            <UsageMetrics />
          </section>
        </main>
      </div>
    </div>
  );
}
