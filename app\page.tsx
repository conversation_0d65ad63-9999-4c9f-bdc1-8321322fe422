import { Link } from "@heroui/link";
import { Button } from "@heroui/button";
import { Spacer } from "@heroui/spacer";

import { title, subtitle } from "@/components/primitives";
import {
  HtmlIcon,
  PdfIcon,
  ResumeIcon,
  CloudIcon,
  SpeedIcon,
  SecurityIcon,
  GlobalIcon,
  RocketIcon,
} from "@/components/icons";

// Additional icons for the new cards
const BarChartIcon = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={className}>
    <path d="M3 13h4v8H3v-8zm6-5h4v13H9V8zm6-5h4v18h-4V3z" fill="currentColor"/>
  </svg>
);

const GlobeIcon = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={className}>
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
    <path d="M2 12h20M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" strokeWidth="2"/>
  </svg>
);

const GitBranchIcon = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={className}>
    <circle cx="6" cy="6" r="3" stroke="currentColor" strokeWidth="2"/>
    <circle cx="18" cy="18" r="3" stroke="currentColor" strokeWidth="2"/>
    <path d="M6 9v9M18 9c0-1.7-1.3-3-3-3h-3" stroke="currentColor" strokeWidth="2"/>
  </svg>
);

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center gap-6 pt-24 pb-16 md:pt-32 md:pb-24 text-center bg-black relative overflow-hidden">
        {/* Refined Grid System - Equal Spacing */}
        <div className="absolute inset-0 opacity-3">
          {/* Horizontal grid lines - Equal 1/6 spacing */}
          <div className="absolute top-1/6 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/80 to-transparent"></div>
          <div className="absolute top-2/6 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-400/70 to-transparent"></div>
          <div className="absolute top-3/6 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/80 to-transparent"></div>
          <div className="absolute top-4/6 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/70 to-transparent"></div>
          <div className="absolute top-5/6 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-400/70 to-transparent"></div>

          {/* Vertical grid lines - Equal 1/6 spacing */}
          <div className="absolute top-0 left-1/6 w-px h-full bg-gradient-to-b from-transparent via-orange-500/80 to-transparent"></div>
          <div className="absolute top-0 left-2/6 w-px h-full bg-gradient-to-b from-transparent via-orange-400/70 to-transparent"></div>
          <div className="absolute top-0 left-3/6 w-px h-full bg-gradient-to-b from-transparent via-red-500/80 to-transparent"></div>
          <div className="absolute top-0 left-4/6 w-px h-full bg-gradient-to-b from-transparent via-orange-500/70 to-transparent"></div>
          <div className="absolute top-0 left-5/6 w-px h-full bg-gradient-to-b from-transparent via-red-400/70 to-transparent"></div>
        </div>

        {/* Large Background Orbs for Visual Flow */}
        <div className="absolute top-1/4 left-1/8 w-80 h-80 bg-gradient-to-br from-orange-500/8 to-red-600/6 rounded-full blur-3xl opacity-60 animate-float"></div>
        <div className="absolute bottom-1/4 right-1/8 w-96 h-96 bg-gradient-to-br from-orange-400/6 to-yellow-500/4 rounded-full blur-3xl opacity-50 animate-float-delayed"></div>

        {/* Central Connecting Orb */}
        <div className="absolute top-3/4 left-1/2 transform -translate-x-1/2 w-[40rem] h-[40rem] bg-gradient-to-br from-orange-500/5 to-red-500/3 rounded-full blur-3xl opacity-40 animate-pulse-slow"></div>

        {/* Refined Accent Elements */}
        <div className="absolute top-1/6 right-1/4 w-32 h-32 bg-gradient-to-br from-orange-400/12 to-red-400/8 rounded-full blur-2xl opacity-60 animate-float-slow"></div>
        <div className="absolute bottom-1/6 left-1/4 w-40 h-40 bg-gradient-to-br from-red-400/10 to-orange-500/6 rounded-full blur-2xl opacity-50 animate-float-reverse"></div>
        <div className="max-w-4xl mx-auto px-6 relative z-20">
          <h1 className={title({ size: "lg" })}>
            Lightning-fast&nbsp;
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-500 font-bold">
              static hosting&nbsp;
            </span>
            <br />
            for modern web applications
          </h1>
          <div className={subtitle({ class: "mt-6 max-w-2xl mx-auto" })}>
            Deploy HTML sites, PDFs, resumes, and documents instantly with zero configuration.
            Global CDN, SSL certificates, and custom domains included.
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8 relative z-20">
          <Button
            size="lg"
            variant="shadow"
            className="font-semibold px-8 py-6 text-lg bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg shadow-orange-500/25"
          >
            <RocketIcon size={20} />
            Deploy Now - Free
          </Button>
          <Button
            size="lg"
            variant="bordered"
            className="font-semibold px-8 py-6 text-lg"
          >
            View Documentation
          </Button>
        </div>

        <div className="mt-12 text-sm text-default-500 relative z-20">
          ⚡ Deploy in seconds • 🌍 Global CDN • 🔒 Free SSL • 📱 Mobile optimized
        </div>
      </section>

      {/* Visual Flow Transition */}
      <div className="relative bg-black">
        {/* Connecting gradient orbs */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[50rem] h-[50rem] bg-gradient-to-br from-orange-500/3 to-red-500/2 rounded-full blur-3xl opacity-30"></div>
        <div className="absolute top-8 left-1/4 w-64 h-64 bg-gradient-to-br from-orange-400/6 to-yellow-500/4 rounded-full blur-3xl opacity-40 animate-pulse-slow"></div>
        <div className="absolute top-8 right-1/4 w-80 h-80 bg-gradient-to-br from-red-400/5 to-orange-500/3 rounded-full blur-3xl opacity-35 animate-float"></div>
      </div>

      {/* Services Section - Bento Grid */}
      <section className="bg-black py-16 sm:py-24 relative overflow-hidden">
        {/* Subtle background elements for continuity */}
        <div className="absolute inset-0 opacity-4">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/50 to-transparent"></div>
          <div className="absolute bottom-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/30 to-transparent"></div>
        </div>

        {/* Background orbs for depth */}
        <div className="absolute top-1/3 right-1/8 w-72 h-72 bg-gradient-to-br from-orange-500/4 to-red-600/2 rounded-full blur-3xl opacity-50 animate-pulse-slow"></div>
        <div className="absolute bottom-1/3 left-1/8 w-80 h-80 bg-gradient-to-br from-red-400/3 to-orange-500/2 rounded-full blur-3xl opacity-40 animate-float"></div>

        <div className="mx-auto max-w-2xl px-6 lg:max-w-7xl lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-center text-sm font-medium text-orange-500 uppercase tracking-wider mb-4">Deploy faster</h2>
            <p className="mx-auto mt-2 max-w-4xl text-center text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
              Perfect hosting for every project
            </p>
            <p className="mx-auto mt-6 max-w-2xl text-center text-lg text-gray-400 leading-relaxed">
              From simple HTML pages to complex documents, we've got you covered with specialized hosting solutions.
            </p>
          </div>

          {/* Bento Grid Layout */}
          <div className="mt-16 grid gap-6 lg:grid-cols-6 lg:grid-rows-3 h-[800px]">

            {/* HTML Hosting - Large Featured Card */}
            <div className="lg:col-span-4 lg:row-span-2 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 overflow-hidden hover:border-orange-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-0 right-0 w-64 h-64 bg-orange-500/10 rounded-full blur-3xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-start gap-6 mb-8">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-orange-500 to-red-600 shadow-lg shadow-orange-500/25 float-animation">
                      <HtmlIcon size={32} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-white mb-3">HTML Hosting</h3>
                      <div className="flex flex-wrap gap-2 mb-4">
                        <span className="px-3 py-1.5 bg-orange-500/20 text-orange-400 text-sm rounded-full font-medium border border-orange-500/30">Most Popular</span>
                        <span className="px-3 py-1.5 bg-emerald-500/20 text-emerald-400 text-sm rounded-full font-medium border border-emerald-500/30">Lightning Fast</span>
                      </div>
                      <p className="text-gray-300 leading-relaxed">
                        Deploy static websites, SPAs, and HTML applications with lightning speed. Perfect for portfolios, landing pages, and modern web applications.
                      </p>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="grid grid-cols-2 gap-4 mb-8">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-sm text-gray-400">Global CDN</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-sm text-gray-400">Auto SSL</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-sm text-gray-400">Git Integration</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-sm text-gray-400">Custom Domains</span>
                    </div>
                  </div>

                  {/* Mock Browser Window */}
                  <div className="flex-1 bg-gray-800/50 rounded-xl border border-gray-700/50 overflow-hidden mb-6">
                    <div className="flex items-center gap-2 bg-gray-900/80 px-4 py-3 border-b border-gray-700/50">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <div className="flex-1 bg-gray-800 rounded px-3 py-1.5 text-xs text-gray-400 ml-4">
                        https://your-site.statichost.com
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      <div className="h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full w-3/4"></div>
                      <div className="h-3 bg-gray-600 rounded-full w-1/2"></div>
                      <div className="h-3 bg-gray-600 rounded-full w-2/3"></div>
                      <div className="h-3 bg-gray-600 rounded-full w-1/3"></div>
                      <div className="grid grid-cols-3 gap-3 mt-6">
                        <div className="h-16 bg-gray-700 rounded-lg"></div>
                        <div className="h-16 bg-gray-700 rounded-lg"></div>
                        <div className="h-16 bg-gray-700 rounded-lg"></div>
                      </div>
                    </div>
                  </div>

                  {/* Buttons */}
                  <div className="flex gap-3">
                    <Button
                      variant="shadow"
                      className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-lg shadow-orange-500/25"
                    >
                      Deploy Now
                    </Button>
                    <Button
                      variant="bordered"
                      className="flex-1 border-gray-600 text-gray-300 hover:border-gray-500 hover:text-white"
                    >
                      View Demo
                    </Button>
                  </div>
                </div>
              </div>
            </div>



            {/* PDF Hosting - Medium Card */}
            <div className="lg:col-span-2 lg:row-span-1 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-orange-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute bottom-0 right-0 w-32 h-32 bg-orange-500/10 rounded-full blur-2xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-start gap-4 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-red-600 shadow-lg shadow-orange-500/25 float-animation">
                      <PdfIcon size={20} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-white mb-2">PDF Hosting</h3>
                      <span className="px-2 py-1 bg-orange-500/20 text-orange-400 text-xs rounded-full font-medium border border-orange-500/30">Secure</span>
                      <p className="text-gray-300 text-sm leading-relaxed mt-3">
                        Share documents, manuals, and reports with secure, fast PDF hosting.
                      </p>
                    </div>
                  </div>

                  {/* PDF Preview Mock */}
                  <div className="flex-1 bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-hidden mb-4">
                    <div className="flex items-center gap-3 bg-gray-900/80 px-3 py-2 border-b border-gray-700/50">
                      <PdfIcon size={12} className="text-orange-400" />
                      <span className="text-xs font-medium text-gray-300">annual-report.pdf</span>
                      <div className="ml-auto flex items-center gap-1">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-gray-500">Protected</span>
                      </div>
                    </div>
                    <div className="p-3 space-y-2">
                      <div className="h-1.5 bg-gradient-to-r from-orange-400 to-red-400 rounded-full w-full"></div>
                      <div className="h-1 bg-gray-600 rounded-full w-4/5"></div>
                      <div className="h-1 bg-gray-600 rounded-full w-3/5"></div>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2 text-xs mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                      <span className="text-gray-400">Password protection</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                      <span className="text-gray-400">Download restrictions</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                      <span className="text-gray-400">Analytics tracking</span>
                    </div>
                  </div>

                  <Button
                    color="warning"
                    variant="flat"
                    className="w-full bg-orange-500/20 text-orange-400 border-orange-500/30 hover:bg-orange-500/30 text-sm"
                    size="sm"
                  >
                    Learn More
                  </Button>
                </div>
              </div>
            </div>
 
            {/* Analytics Card - New Row */}
            <div className="lg:col-span-2 lg:row-span-1 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-indigo-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-0 right-0 w-24 h-24 bg-indigo-500/10 rounded-full blur-2xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2.5 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25">
                      <BarChartIcon size={20} className="text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white mb-1">Analytics</h3>
                      <span className="px-2 py-0.5 bg-indigo-500/20 text-indigo-400 text-xs rounded-full font-medium border border-indigo-500/30">Real-time</span>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm leading-relaxed mb-4">
                    Track visitors, performance, and engagement with detailed analytics.
                  </p>

                  {/* Chart Mock */}
                  <div className="flex-1 bg-gray-800/50 rounded-lg border border-gray-700/50 p-3 mb-4">
                    <div className="flex items-end justify-between h-16 gap-1">
                      <div className="bg-indigo-500 rounded-sm w-3 h-8"></div>
                      <div className="bg-indigo-400 rounded-sm w-3 h-12"></div>
                      <div className="bg-indigo-500 rounded-sm w-3 h-6"></div>
                      <div className="bg-indigo-400 rounded-sm w-3 h-16"></div>
                      <div className="bg-indigo-500 rounded-sm w-3 h-10"></div>
                      <div className="bg-indigo-400 rounded-sm w-3 h-14"></div>
                      <div className="bg-indigo-500 rounded-sm w-3 h-8"></div>
                    </div>
                  </div>

                  <Button
                    color="secondary"
                    variant="flat"
                    size="sm"
                    className="w-full bg-indigo-500/20 text-indigo-400 border-indigo-500/30 hover:bg-indigo-500/30"
                  >
                    View Analytics
                  </Button>
                </div>
              </div>
            </div>

            {/* Custom Domains Card */}
            <div className="lg:col-span-2 lg:row-span-1 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-yellow-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 via-transparent to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-yellow-500/10 rounded-full blur-2xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2.5 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-600 shadow-lg shadow-yellow-500/25">
                      <GlobeIcon size={20} className="text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white mb-1">Custom Domains</h3>
                      <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-400 text-xs rounded-full font-medium border border-yellow-500/30">Free SSL</span>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm leading-relaxed mb-4">
                    Use your own domain with automatic SSL certificates and DNS management.
                  </p>

                  {/* Domain Mock */}
                  <div className="flex-1 bg-gray-800/50 rounded-lg border border-gray-700/50 p-3 mb-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-gray-300">yourdomain.com</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-gray-300">www.yourdomain.com</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-xs text-gray-400">SSL Certificate</span>
                      </div>
                    </div>
                  </div>

                  <Button
                    color="warning"
                    variant="flat"
                    size="sm"
                    className="w-full bg-yellow-500/20 text-yellow-400 border-yellow-500/30 hover:bg-yellow-500/30"
                  >
                    Add Domain
                  </Button>
                </div>
              </div>
            </div>

            {/* Git Integration Card */}
            <div className="lg:col-span-2 lg:row-span-1 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-gray-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-500/5 via-transparent to-slate-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-0 left-0 w-24 h-24 bg-gray-500/10 rounded-full blur-2xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2.5 rounded-xl bg-gradient-to-br from-gray-600 to-slate-700 shadow-lg shadow-gray-500/25">
                      <GitBranchIcon size={20} className="text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white mb-1">Git Integration</h3>
                      <span className="px-2 py-0.5 bg-gray-500/20 text-gray-400 text-xs rounded-full font-medium border border-gray-500/30">Auto Deploy</span>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm leading-relaxed mb-4">
                    Connect your GitHub, GitLab, or Bitbucket repository for automatic deployments.
                  </p>

                  {/* Git Mock */}
                  <div className="flex-1 bg-gray-800/50 rounded-lg border border-gray-700/50 p-3 mb-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-gray-300">main branch</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-xs text-gray-400">Auto deploy on push</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="text-xs text-gray-400">Preview branches</span>
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="flat"
                    size="sm"
                    className="w-full bg-gray-500/20 text-gray-400 border-gray-500/30 hover:bg-gray-500/30"
                  >
                    Connect Git
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* Features Section */}
      <section className="py-24 px-6 bg-black">

        <div className="max-w-6xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Why choose&nbsp;
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-500">
                StaticHost?
              </span>
            </h2>
            <p className="text-lg text-gray-400 max-w-2xl mx-auto leading-relaxed">
              Built for speed, security, and simplicity. Everything you need to get your content online fast.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-orange-500/20 to-red-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-orange-500/30 group-hover:border-orange-500/50 transition-all duration-300">
                <SpeedIcon size={32} className="text-orange-500" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Lightning Fast</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Global CDN ensures your content loads instantly worldwide
              </p>
            </div>

            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-green-500/20 to-emerald-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-green-500/30 group-hover:border-green-500/50 transition-all duration-300">
                <SecurityIcon size={32} className="text-green-400" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Secure by Default</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Free SSL certificates and DDoS protection included
              </p>
            </div>

            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500/20 to-cyan-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-blue-500/30 group-hover:border-blue-500/50 transition-all duration-300">
                <GlobalIcon size={32} className="text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Global Reach</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Deployed across 200+ edge locations worldwide
              </p>
            </div>

            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-orange-500/20 to-yellow-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-orange-500/30 group-hover:border-orange-500/50 transition-all duration-300">
                <CloudIcon size={32} className="text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Zero Config</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Deploy with a simple drag & drop or Git integration
              </p>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* CTA Section */}
      <section className="py-24 px-6 bg-black">

        <div className="max-w-4xl mx-auto text-center relative z-10">
          <div className="bg-gradient-to-br from-[#0f0a14]/95 to-[#1a0f1f]/90 backdrop-blur-xl border border-purple-800/40 rounded-3xl p-12 shadow-2xl shadow-[#b249f8]/10">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to deploy your&nbsp;
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-500">
                next project?
              </span>
            </h2>
            <p className="text-lg text-gray-400 mt-4 mb-8 max-w-2xl mx-auto leading-relaxed">
              Join thousands of developers who trust StaticHost for their hosting needs.
              Start with our free tier and scale as you grow.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button
                size="lg"
                variant="shadow"
                className="font-semibold px-8 py-6 text-lg bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg shadow-orange-500/25"
              >
                <RocketIcon size={20} />
                Start Free Trial
              </Button>
              <Button
                size="lg"
                variant="bordered"
                className="font-semibold px-8 py-6 text-lg border-gray-600 text-gray-300 hover:border-gray-500 hover:text-white"
                as={Link}
                href="/pricing"
              >
                View Pricing
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                No credit card required
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Free SSL & CDN
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                24/7 Support
              </div>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* Stats Section */}
      <section className="py-24 px-6 bg-black border-t border-gray-800/50">
        <div className="max-w-6xl mx-auto relative z-10">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-orange-500 mb-2 group-hover:scale-110 transition-transform duration-300">99.9%</div>
              <div className="text-gray-400">Uptime SLA</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2 group-hover:scale-110 transition-transform duration-300">200+</div>
              <div className="text-gray-400">Edge Locations</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform duration-300">10M+</div>
              <div className="text-gray-400">Sites Hosted</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-cyan-400 mb-2 group-hover:scale-110 transition-transform duration-300">&lt;100ms</div>
              <div className="text-gray-400">Average Response</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
