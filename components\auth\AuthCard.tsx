import { <PERSON>, <PERSON>H<PERSON>er, CardBody } from "@heroui/card";

interface AuthCardProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
}

export function AuthCard({ title, subtitle, children }: AuthCardProps) {
  return (
    <div className="relative w-full max-w-md mx-auto">
      {/* Background grid pattern from homepage */}
      <div className="absolute inset-0 opacity-[0.08] pointer-events-none">
        {/* Horizontal lines */}
        <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/60 to-transparent"></div>
        <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/60 to-transparent"></div>
        <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-400/60 to-transparent"></div>
        
        {/* Vertical lines */}
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-orange-500/60 to-transparent"></div>
        <div className="absolute top-0 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-red-500/60 to-transparent"></div>
        <div className="absolute top-0 left-3/4 w-px h-full bg-gradient-to-b from-transparent via-orange-400/60 to-transparent"></div>
      </div>
      
      {/* Floating orbs for visual appeal */}
      <div className="absolute -top-8 -left-8 w-24 h-24 bg-gradient-to-br from-orange-500/10 to-red-500/5 rounded-full blur-xl opacity-70 animate-pulse"></div>
      <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-gradient-to-br from-red-500/8 to-orange-400/4 rounded-full blur-xl opacity-60 animate-pulse" style={{animationDelay: '1s'}}></div>
      
      <Card className="relative bg-gradient-to-br from-black/90 via-gray-900/95 to-black/90 backdrop-blur-2xl border border-orange-500/20 shadow-2xl shadow-orange-500/20 w-full overflow-hidden">
        {/* Subtle inner glow */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5 pointer-events-none"></div>
        
        {/* Top accent line */}
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/80 to-transparent"></div>
        
        <CardHeader className="relative flex flex-col gap-2 pb-4 pt-6">
          <div className="text-center space-y-1">
            <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-500/30 mb-2">
              <div className="w-5 h-5 rounded-full bg-gradient-to-br from-orange-400 to-red-500"></div>
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">{title}</h1>
            {subtitle && (
              <p className="text-gray-400 text-xs leading-relaxed max-w-xs mx-auto">{subtitle}</p>
            )}
          </div>
        </CardHeader>
        
        <CardBody className="relative pt-0 pb-6 px-6">
          {children}
        </CardBody>
        
        {/* Bottom accent line */}
        <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/60 to-transparent"></div>
      </Card>
    </div>
  );
}
