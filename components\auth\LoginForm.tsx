"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { Link } from "@heroui/link";
import { EyeIcon, EyeSlashIcon } from "@/components/icons";
import { loginSchema, type LoginFormData } from "@/lib/validations";

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

export function LoginForm({ onSubmit, isLoading = false, error }: LoginFormProps) {
  const [isVisible, setIsVisible] = useState(false);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const toggleVisibility = () => setIsVisible(!isVisible);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <div className="relative p-4 rounded-xl bg-gradient-to-r from-red-500/10 to-red-600/10 border border-red-500/30 text-red-300 text-sm backdrop-blur-sm">
          <div className="absolute inset-0 bg-red-500/5 rounded-xl"></div>
          <div className="relative flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-red-400"></div>
            {error}
          </div>
        </div>
      )}

      <div className="space-y-5">
        <div className="relative">
          <Input
            {...register("email")}
            type="email"
            label="Email"
            placeholder="Enter your email"
            variant="bordered"
            isInvalid={!!errors.email}
            errorMessage={errors.email?.message}
            classNames={{
              input: "text-white bg-transparent",
              inputWrapper: "border-gray-600/50 hover:border-orange-500/60 focus-within:border-orange-500/80 bg-black/20 backdrop-blur-sm transition-all duration-300",
              label: "text-gray-300 font-medium",
              errorMessage: "text-red-400"
            }}
          />
        </div>

        <div className="relative">
          <Input
            {...register("password")}
            label="Password"
            placeholder="Enter your password"
            variant="bordered"
            isInvalid={!!errors.password}
            errorMessage={errors.password?.message}
            endContent={
              <button
                className="focus:outline-none text-gray-400 hover:text-orange-400 transition-colors"
                type="button"
                onClick={toggleVisibility}
              >
                {isVisible ? (
                  <EyeSlashIcon className="text-xl pointer-events-none" />
                ) : (
                  <EyeIcon className="text-xl pointer-events-none" />
                )}
              </button>
            }
            type={isVisible ? "text" : "password"}
            classNames={{
              input: "text-white bg-transparent",
              inputWrapper: "border-gray-600/50 hover:border-orange-500/60 focus-within:border-orange-500/80 bg-black/20 backdrop-blur-sm transition-all duration-300",
              label: "text-gray-300 font-medium",
              errorMessage: "text-red-400"
            }}
          />
        </div>
      </div>

      <div className="flex items-center justify-end pt-2">
        <Link
          href="/forgot-password"
          className="text-sm text-orange-400 hover:text-orange-300 transition-colors font-medium hover:underline"
        >
          Forgot password?
        </Link>
      </div>

      <Button
        type="submit"
        className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-xl shadow-orange-500/30 border border-orange-500/20 transition-all duration-300 hover:shadow-orange-500/40 hover:scale-[1.02]"
        size="lg"
        isLoading={isLoading}
        disabled={isLoading}
      >
        {isLoading ? "Signing in..." : "Sign In"}
      </Button>

      <div className="text-center pt-4">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-700/50"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-4 bg-gradient-to-r from-black via-gray-900 to-black text-gray-400">
              Don't have an account?
            </span>
          </div>
        </div>
        <div className="mt-4">
          <Link
            href="/register"
            className="inline-flex items-center justify-center px-6 py-2 text-sm font-medium text-orange-400 hover:text-white border border-orange-500/30 hover:border-orange-500/60 rounded-lg hover:bg-orange-500/10 transition-all duration-300"
          >
            Create Account
          </Link>
        </div>
      </div>
    </form>
  );
}
