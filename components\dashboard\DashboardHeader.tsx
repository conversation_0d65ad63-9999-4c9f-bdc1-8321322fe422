"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Avatar } from "@heroui/avatar";
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/dropdown";
import { Badge } from "@heroui/badge";
import { BellIcon, SettingsIcon, LogOutIcon, UserIcon } from "@/components/icons";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

interface User {
  id: string;
  email: string;
  name: string | null;
}

interface DashboardHeaderProps {
  className?: string;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({ className }) => {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/auth/status');
      const data = await response.json();
      if (data.authenticated) {
        setUser(data.user);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };
  return (
    <header className={`bg-black border-b border-gray-800/50 ${className || ""}`}>
      <div className="container mx-auto px-6 py-4 max-w-7xl">
        <div className="flex items-center justify-between">
          {/* Logo and Navigation */}
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">S</span>
              </div>
              <span className="text-white font-bold text-xl">StaticHost</span>
            </Link>

            <nav className="hidden md:flex items-center gap-6">
              <Link href="/dashboard" className="text-orange-400 font-medium">
                Dashboard
              </Link>
              <Link href="/dashboard/projects" className="text-gray-400 hover:text-white transition-colors">
                Projects
              </Link>
              <Link href="/dashboard/analytics" className="text-gray-400 hover:text-white transition-colors">
                Analytics
              </Link>
              <Link href="/dashboard/domains" className="text-gray-400 hover:text-white transition-colors">
                Domains
              </Link>
            </nav>
          </div>

          {/* User Actions */}
          <div className="flex items-center gap-4">
            {/* Notifications */}
            <Button
              isIconOnly
              variant="light"
              className="text-gray-400 hover:text-white"
              aria-label="Notifications"
            >
              <Badge content="3" color="danger" size="sm">
                <BellIcon size={20} />
              </Badge>
            </Button>

            {/* Settings */}
            <Button
              isIconOnly
              variant="light"
              className="text-gray-400 hover:text-white"
              aria-label="Settings"
              as={Link}
              href="/dashboard/settings"
            >
              <SettingsIcon size={20} />
            </Button>

            {/* User Menu */}
            <Dropdown placement="bottom-end">
              <DropdownTrigger>
                <Avatar
                  as="button"
                  className="transition-transform hover:scale-105"
                  color="primary"
                  name={user?.name || user?.email || "User"}
                  size="sm"
                  src={`https://i.pravatar.cc/150?u=${user?.email || '<EMAIL>'}`}
                />
              </DropdownTrigger>
              <DropdownMenu aria-label="User menu actions" variant="flat">
                <DropdownItem key="profile" startContent={<UserIcon size={16} />}>
                  Profile
                </DropdownItem>
                <DropdownItem key="settings" startContent={<SettingsIcon size={16} />}>
                  Settings
                </DropdownItem>
                <DropdownItem 
                  key="logout" 
                  color="danger" 
                  startContent={<LogOutIcon size={16} />}
                  onClick={handleLogout}
                >
                  Log Out
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
    </header>
  );
};
