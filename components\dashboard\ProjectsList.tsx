"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, CardHeader } from "@heroui/card";
import { <PERSON><PERSON> } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Avatar } from "@heroui/avatar";
import { 
  GlobeIcon, 
  SettingsIcon, 
  BarChartIcon,
  ExternalLinkIcon,
  GitBranchIcon
} from "@/components/icons";

interface Project {
  id: string;
  name: string;
  domain: string;
  status: "active" | "building" | "error" | "paused";
  lastDeployed: string;
  framework: string;
  visitors: string;
  repository?: string;
}

interface ProjectCardProps {
  project: Project;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project }) => {
  const getStatusColor = (status: Project["status"]) => {
    switch (status) {
      case "active":
        return "success";
      case "building":
        return "warning";
      case "error":
        return "danger";
      case "paused":
        return "default";
      default:
        return "default";
    }
  };

  const getStatusText = (status: Project["status"]) => {
    switch (status) {
      case "active":
        return "Live";
      case "building":
        return "Building";
      case "error":
        return "Error";
      case "paused":
        return "Paused";
      default:
        return "Unknown";
    }
  };

  return (
    <Card className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 hover:border-orange-500/30 transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between w-full">
          <div className="flex items-start gap-3">
            <Avatar
              name={project.name.charAt(0).toUpperCase()}
              size="sm"
              className="bg-gradient-to-br from-orange-500 to-red-600 text-white"
            />
            <div>
              <h3 className="text-lg font-semibold text-white">{project.name}</h3>
              <p className="text-gray-400 text-sm">{project.domain}</p>
            </div>
          </div>
          <Chip
            size="sm"
            color={getStatusColor(project.status)}
            variant="flat"
          >
            {getStatusText(project.status)}
          </Chip>
        </div>
      </CardHeader>
      
      <CardBody className="pt-0">
        <div className="space-y-4">
          {/* Project Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-500">Framework</p>
              <p className="text-gray-300 font-medium">{project.framework}</p>
            </div>
            <div>
              <p className="text-gray-500">Visitors (30d)</p>
              <p className="text-gray-300 font-medium">{project.visitors}</p>
            </div>
          </div>

          {/* Repository */}
          {project.repository && (
            <div className="flex items-center gap-2 text-sm">
              <GitBranchIcon size={16} className="text-gray-500" />
              <span className="text-gray-400">{project.repository}</span>
            </div>
          )}

          {/* Last Deployed */}
          <div className="text-sm">
            <p className="text-gray-500">Last deployed</p>
            <p className="text-gray-300">{project.lastDeployed}</p>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 pt-2">
            <Button
              size="sm"
              variant="flat"
              className="bg-orange-500/20 text-orange-400 border-orange-500/30 hover:bg-orange-500/30"
              startContent={<ExternalLinkIcon size={14} />}
            >
              Visit
            </Button>
            <Button
              size="sm"
              variant="light"
              className="text-gray-400 hover:text-white"
              isIconOnly
              aria-label="Analytics"
            >
              <BarChartIcon size={16} />
            </Button>
            <Button
              size="sm"
              variant="light"
              className="text-gray-400 hover:text-white"
              isIconOnly
              aria-label="Settings"
            >
              <SettingsIcon size={16} />
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

interface ProjectsListProps {
  className?: string;
}

export const ProjectsList: React.FC<ProjectsListProps> = ({ className }) => {
  const projects: Project[] = [
    {
      id: "1",
      name: "Portfolio Website",
      domain: "john-doe.statichost.com",
      status: "active",
      lastDeployed: "2 hours ago",
      framework: "Next.js",
      visitors: "1.2K",
      repository: "github.com/johndoe/portfolio"
    },
    {
      id: "2",
      name: "Company Landing",
      domain: "acme-corp.com",
      status: "active",
      lastDeployed: "1 day ago",
      framework: "React",
      visitors: "5.8K",
      repository: "github.com/acme/landing"
    },
    {
      id: "3",
      name: "Documentation Site",
      domain: "docs.myapp.com",
      status: "building",
      lastDeployed: "3 days ago",
      framework: "Docusaurus",
      visitors: "892",
      repository: "github.com/myapp/docs"
    },
    {
      id: "4",
      name: "Blog",
      domain: "blog.example.com",
      status: "active",
      lastDeployed: "5 days ago",
      framework: "Gatsby",
      visitors: "2.1K"
    }
  ];

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Projects</h2>
          <p className="text-gray-400">Manage your hosted sites and applications</p>
        </div>
        <Button
          className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold"
          startContent={<GlobeIcon size={16} />}
        >
          New Project
        </Button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {projects.map((project) => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>
    </div>
  );
};
