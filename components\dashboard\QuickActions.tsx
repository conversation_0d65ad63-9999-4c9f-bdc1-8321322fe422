"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { 
  RocketIcon, 
  PlusIcon, 
  SettingsIcon, 
  GlobeIcon,
  GitBranchIcon,
  BarChartIcon
} from "@/components/icons";

interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick?: () => void;
  href?: string;
  variant?: "primary" | "secondary";
}

const QuickActionCard: React.FC<QuickActionProps> = ({ 
  title, 
  description, 
  icon, 
  onClick, 
  href,
  variant = "secondary" 
}) => {
  const isPrimary = variant === "primary";
  
  return (
    <Card className={`
      bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border 
      ${isPrimary 
        ? "border-orange-500/50 hover:border-orange-500/70" 
        : "border-gray-700/50 hover:border-orange-500/30"
      } 
      transition-all duration-300 cursor-pointer group
    `}>
      <CardBody className="p-6">
        <div className="flex items-start gap-4">
          <div className={`
            p-3 rounded-xl border transition-all duration-300
            ${isPrimary 
              ? "bg-gradient-to-br from-orange-500 to-red-600 border-orange-500/30 shadow-lg shadow-orange-500/25" 
              : "bg-gradient-to-br from-orange-500/20 to-red-600/20 border-orange-500/30 group-hover:from-orange-500/30 group-hover:to-red-600/30"
            }
          `}>
            {icon}
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-1 group-hover:text-orange-400 transition-colors">
              {title}
            </h3>
            <p className="text-gray-400 text-sm leading-relaxed">
              {description}
            </p>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

interface QuickActionsProps {
  className?: string;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ className }) => {
  const actions = [
    {
      title: "Deploy New Project",
      description: "Upload files or connect your Git repository to deploy a new static site",
      icon: <RocketIcon size={24} className="text-white" />,
      variant: "primary" as const,
      onClick: () => console.log("Deploy new project")
    },
    {
      title: "Create Project",
      description: "Start a new project from scratch or use one of our templates",
      icon: <PlusIcon size={24} className="text-orange-500" />,
      onClick: () => console.log("Create project")
    },
    {
      title: "Manage Domains",
      description: "Add custom domains, configure DNS, and manage SSL certificates",
      icon: <GlobeIcon size={24} className="text-orange-500" />,
      onClick: () => console.log("Manage domains")
    },
    {
      title: "Connect Git",
      description: "Link your GitHub, GitLab, or Bitbucket repository for auto-deployments",
      icon: <GitBranchIcon size={24} className="text-orange-500" />,
      onClick: () => console.log("Connect Git")
    },
    {
      title: "View Analytics",
      description: "Monitor your site's performance, traffic, and user engagement",
      icon: <BarChartIcon size={24} className="text-orange-500" />,
      onClick: () => console.log("View analytics")
    },
    {
      title: "Account Settings",
      description: "Update your profile, billing information, and account preferences",
      icon: <SettingsIcon size={24} className="text-orange-500" />,
      onClick: () => console.log("Account settings")
    }
  ];

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Quick Actions</h2>
        <p className="text-gray-400">Common tasks and shortcuts to get things done faster</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {actions.map((action, index) => (
          <QuickActionCard key={index} {...action} />
        ))}
      </div>
    </div>
  );
};
