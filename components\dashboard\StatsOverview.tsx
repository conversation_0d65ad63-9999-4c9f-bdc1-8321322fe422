"use client";

import { Card, CardBody } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { 
  GlobeIcon, 
  SpeedIcon, 
  CloudIcon, 
  BarChartIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from "@/components/icons";

interface StatCardProps {
  title: string;
  value: string;
  change: string;
  changeType: "increase" | "decrease" | "neutral";
  icon: React.ReactNode;
  description?: string;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  change, 
  changeType, 
  icon, 
  description 
}) => {
  const getChangeColor = () => {
    switch (changeType) {
      case "increase":
        return "text-green-400";
      case "decrease":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  const getChangeIcon = () => {
    switch (changeType) {
      case "increase":
        return <TrendingUpIcon size={14} />;
      case "decrease":
        return <TrendingDownIcon size={14} />;
      default:
        return null;
    }
  };

  return (
    <Card className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 hover:border-orange-500/30 transition-all duration-300">
      <CardBody className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500/20 to-red-600/20 border border-orange-500/30">
            {icon}
          </div>
          <Chip
            size="sm"
            variant="flat"
            className={`${getChangeColor()} bg-transparent`}
            startContent={getChangeIcon()}
          >
            {change}
          </Chip>
        </div>
        
        <div>
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-1">{value}</h3>
          <p className="text-gray-400 text-sm font-medium">{title}</p>
          {description && (
            <p className="text-gray-500 text-xs mt-2">{description}</p>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

interface StatsOverviewProps {
  className?: string;
}

export const StatsOverview: React.FC<StatsOverviewProps> = ({ className }) => {
  const stats = [
    {
      title: "Total Projects",
      value: "12",
      change: "+2 this month",
      changeType: "increase" as const,
      icon: <GlobeIcon size={24} className="text-orange-500" />,
      description: "Active hosted projects"
    },
    {
      title: "Monthly Visitors",
      value: "24.5K",
      change: "+12.3%",
      changeType: "increase" as const,
      icon: <BarChartIcon size={24} className="text-orange-500" />,
      description: "Unique visitors this month"
    },
    {
      title: "Avg Load Time",
      value: "1.2s",
      change: "-0.3s",
      changeType: "increase" as const,
      icon: <SpeedIcon size={24} className="text-orange-500" />,
      description: "Global average response time"
    },
    {
      title: "Storage Used",
      value: "2.1GB",
      change: "+156MB",
      changeType: "neutral" as const,
      icon: <CloudIcon size={24} className="text-orange-500" />,
      description: "of 10GB total storage"
    }
  ];

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Overview</h2>
        <p className="text-gray-400">Your hosting performance at a glance</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>
    </div>
  );
};
