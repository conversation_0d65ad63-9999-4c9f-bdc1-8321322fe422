"use client";

import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { Progress } from "@heroui/progress";
import { Chip } from "@heroui/chip";
import { 
  CloudIcon, 
  SpeedIcon, 
  BarChartIcon,
  TrendingUpIcon,
  DatabaseIcon,
  GlobeIcon
} from "@/components/icons";

interface MetricCardProps {
  title: string;
  value: string;
  total: string;
  percentage: number;
  icon: React.ReactNode;
  trend?: string;
  trendType?: "up" | "down" | "neutral";
  description?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  total, 
  percentage, 
  icon, 
  trend,
  trendType = "neutral",
  description 
}) => {
  const getProgressColor = () => {
    if (percentage >= 80) return "danger";
    if (percentage >= 60) return "warning";
    return "success";
  };

  const getTrendColor = () => {
    switch (trendType) {
      case "up":
        return "text-green-400";
      case "down":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  return (
    <Card className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 hover:border-orange-500/30 transition-all duration-300">
      <CardBody className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500/20 to-red-600/20 border border-orange-500/30">
            {icon}
          </div>
          {trend && (
            <Chip
              size="sm"
              variant="flat"
              className={`${getTrendColor()} bg-transparent`}
              startContent={trendType === "up" ? <TrendingUpIcon size={12} /> : null}
            >
              {trend}
            </Chip>
          )}
        </div>
        
        <div className="space-y-3">
          <div>
            <h3 className="text-lg font-semibold text-white mb-1">{title}</h3>
            {description && (
              <p className="text-gray-500 text-xs">{description}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-white">{value}</span>
              <span className="text-gray-400 text-sm">of {total}</span>
            </div>
            
            <Progress
              value={percentage}
              color={getProgressColor()}
              className="w-full"
              size="sm"
            />
            
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">{percentage}% used</span>
              <span className="text-gray-500">{100 - percentage}% remaining</span>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

interface UsageMetricsProps {
  className?: string;
}

export const UsageMetrics: React.FC<UsageMetricsProps> = ({ className }) => {
  const metrics = [
    {
      title: "Storage",
      value: "2.1GB",
      total: "10GB",
      percentage: 21,
      icon: <DatabaseIcon size={24} className="text-orange-500" />,
      trend: "+156MB",
      trendType: "up" as const,
      description: "Files, images, and assets"
    },
    {
      title: "Bandwidth",
      value: "45.2GB",
      total: "100GB",
      percentage: 45,
      icon: <GlobeIcon size={24} className="text-orange-500" />,
      trend: "+12.3GB",
      trendType: "up" as const,
      description: "Data transfer this month"
    },
    {
      title: "Build Minutes",
      value: "127",
      total: "500",
      percentage: 25,
      icon: <SpeedIcon size={24} className="text-orange-500" />,
      trend: "+23 min",
      trendType: "up" as const,
      description: "Deployment build time used"
    },
    {
      title: "API Requests",
      value: "8.4K",
      total: "25K",
      percentage: 34,
      icon: <BarChartIcon size={24} className="text-orange-500" />,
      trend: "+1.2K",
      trendType: "up" as const,
      description: "Analytics and webhook calls"
    }
  ];

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Usage & Limits</h2>
        <p className="text-gray-400">Monitor your resource consumption and plan limits</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>
      
      {/* Upgrade Notice */}
      <Card className="mt-6 bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/30">
        <CardBody className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white mb-1">Need more resources?</h3>
              <p className="text-gray-400 text-sm">Upgrade your plan to get higher limits and premium features</p>
            </div>
            <button className="px-6 py-2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold rounded-lg transition-all duration-300">
              Upgrade Plan
            </button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
