// Simple session management for demo purposes
// In a production app, you would use a more robust solution like NextAuth.js or similar

export interface User {
  id: string;
  email: string;
  name: string | null;
}

// Simple in-memory session store (for demo purposes only)
// In production, use Redis, database sessions, or JWT tokens
const sessions = new Map<string, User>();

export function createSession(user: User): string {
  const sessionId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  sessions.set(sessionId, user);
  return sessionId;
}

export function getSession(sessionId: string): User | null {
  return sessions.get(sessionId) || null;
}

export function deleteSession(sessionId: string): void {
  sessions.delete(sessionId);
}

export function isAuthenticated(sessionId?: string): boolean {
  if (!sessionId) return false;
  return sessions.has(sessionId);
}
