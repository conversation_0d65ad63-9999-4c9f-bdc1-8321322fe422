import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { isAuthenticated } from '@/lib/session'

export function middleware(request: NextRequest) {
  // Check if the request is for the dashboard
  if (request.nextUrl.pathname.startsWith('/dashboard')) {
    // Get the session cookie
    const sessionId = request.cookies.get('sessionId')?.value
    
    // If no session or invalid session, redirect to login
    if (!sessionId || !isAuthenticated(sessionId)) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: '/dashboard/:path*'
}