@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.card-hover-effect:hover {
  transform: perspective(1000px) rotateX(2deg);
}

@media (hover: hover) and (pointer: fine) {
  .card-hover-effect:hover {
    transform: perspective(1000px) rotateX(2deg) translateY(-8px);
  }
}

/* Touch device optimizations */
@media (hover: none) {
  .card-hover-effect:active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}

/* Gradient animation */
.gradient-animate {
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
